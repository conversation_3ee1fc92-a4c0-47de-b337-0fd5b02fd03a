#!/bin/bash

# Test script for the new time range API

echo "Testing new Dune projects time range API..."

# Set base URL (adjust if needed)
BASE_URL="http://localhost:8080/api"

# Test 1: Test with missing parameters
echo "\n1. Testing with missing parameters (should return 400):"
curl -s "$BASE_URL/dune/projects/data" | jq .

# Test 2: Test with invalid start_time
echo "\n2. Testing with invalid start_time (should return 400):"
curl -s "$BASE_URL/dune/projects/data?start_time=invalid&end_time=1703980800" | jq .

# Test 3: Test with invalid end_time
echo "\n3. Testing with invalid end_time (should return 400):"
curl -s "$BASE_URL/dune/projects/data?start_time=1703894400&end_time=invalid" | jq .

# Test 4: Test with end_time before start_time
echo "\n4. Testing with end_time before start_time (should return 400):"
curl -s "$BASE_URL/dune/projects/data?start_time=1703980800&end_time=1703894400" | jq .

# Test 5: Test with valid time range (7 days ago to today)
echo "\n5. Testing with valid time range (7 days ago to today):"
START_TIME=$(date -d '7 days ago' +%s)
END_TIME=$(date +%s)
echo "Start time: $START_TIME ($(date -d @$START_TIME))"
echo "End time: $END_TIME ($(date -d @$END_TIME))"
curl -s "$BASE_URL/dune/projects/data?start_time=$START_TIME&end_time=$END_TIME" | jq .

# Test 6: Test with yesterday's data (for comparison with old API)
echo "\n6. Testing yesterday's data (old API for comparison):"
curl -s "$BASE_URL/dune/projects/yesterday" | jq .

# Test 7: Test with specific date range (last week)
echo "\n7. Testing with last week's data:"
START_TIME=$(date -d '14 days ago' +%s)
END_TIME=$(date -d '7 days ago' +%s)
echo "Start time: $START_TIME ($(date -d @$START_TIME))"
echo "End time: $END_TIME ($(date -d @$END_TIME))"
curl -s "$BASE_URL/dune/projects/data?start_time=$START_TIME&end_time=$END_TIME" | jq .

echo "\nTest completed!"
echo "\nNote: Make sure the server is running on $BASE_URL before running this test."
echo "You can start the server with: make run or go run cmd/main.go"