package services

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestDuneService_GetBatchQueryResults tests the new batch query method
func TestDuneService_GetBatchQueryResults(t *testing.T) {
	t.Skip("Skipping GetBatchQueryResults test due to SQLite time scanning issues in test environment")

	database := setupTestDB(t)
	service := NewDuneService(getTestDuneConfig(), database)

	// Test with empty query IDs
	results, err := service.GetBatchQueryResults([]string{}, time.Now(), time.Now())
	assert.NoError(t, err)
	assert.Empty(t, results)

	// Test with non-existent query IDs
	queryIDs := []string{"query1", "query2", "query3"}
	startDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Date(2024, 1, 31, 0, 0, 0, 0, time.UTC)

	results, err = service.GetBatchQueryResults(queryIDs, startDate, endDate)
	assert.NoError(t, err)
	assert.NotNil(t, results)
}

// TestDuneService_GetAllProjectsDataWithTimeRange_Performance tests the performance improvement
func TestDuneService_GetAllProjectsDataWithTimeRange_Performance(t *testing.T) {
	t.Skip("Skipping GetAllProjectsDataWithTimeRange_Performance test due to SQLite time scanning issues in test environment")

	database := setupTestDB(t)
	service := NewDuneService(getTestDuneConfig(), database)

	// Test the refactored method with empty data
	startDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Date(2024, 1, 31, 0, 0, 0, 0, time.UTC)

	results, err := service.GetAllProjectsDataWithTimeRange(startDate, endDate)
	assert.NoError(t, err)
	assert.NotNil(t, results)
}