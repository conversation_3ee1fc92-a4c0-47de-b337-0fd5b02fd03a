package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"real-time-ca-service/internal/config"
	"time"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"real-time-ca-service/internal/db"
)

// DuneService handles Dune query management and data fetching
type DuneService struct {
	db     *db.Database
	config DuneConfig
}

// DuneConfig holds configuration for Dune service
type DuneConfig struct {
	APIKey     string
	BaseURL    string
	Timeout    time.Duration
	MaxRetries int
	RetryDelay time.Duration
}

// NewDuneService creates a new DuneService
func NewDuneService(conf config.DuneConfig, database *db.Database) *DuneService {
	return &DuneService{
		db: database,
		config: DuneConfig{
			APIKey:     conf.APIKey,
			BaseURL:    conf.BaseURL,
			Timeout:    conf.Timeout,
			MaxRetries: conf.MaxRetries,
			RetryDelay: conf.RetryDelay,
		},
	}
}

// https://api.dune.com/api/v1/query/5399608/results?limit=1000
type DuneQueryResults struct {
	ExecutionId         string    `json:"execution_id"`
	QueryId             int       `json:"query_id"`
	IsExecutionFinished bool      `json:"is_execution_finished"`
	State               string    `json:"state"`
	SubmittedAt         time.Time `json:"submitted_at"`
	ExpiresAt           time.Time `json:"expires_at"`
	ExecutionStartedAt  time.Time `json:"execution_started_at"`
	ExecutionEndedAt    time.Time `json:"execution_ended_at"`
	Result              struct {
		Rows []struct {
			ContractInteraction int    `json:"Contract Interaction"`
			Date                string `json:"Date"`
			Users               int    `json:"Users"`
		} `json:"rows"`
		Metadata struct {
			ColumnNames         []string `json:"column_names"`
			ColumnTypes         []string `json:"column_types"`
			RowCount            int      `json:"row_count"`
			ResultSetBytes      int      `json:"result_set_bytes"`
			TotalRowCount       int      `json:"total_row_count"`
			TotalResultSetBytes int      `json:"total_result_set_bytes"`
			DatapointCount      int      `json:"datapoint_count"`
			PendingTimeMillis   int      `json:"pending_time_millis"`
			ExecutionTimeMillis int      `json:"execution_time_millis"`
		} `json:"metadata"`
	} `json:"result"`
}

// CreateBinding creates a new Twitter-Dune query binding
func (s *DuneService) CreateBinding(binding *db.DuneTwitterBinding) error {
	// Check if Twitter username already exists
	var existingByTwitter db.DuneTwitterBinding
	err := s.db.DB.Where("twitter_user_name = ?", binding.TwitterUserName).First(&existingByTwitter).Error
	if err == nil {
		return fmt.Errorf("twitter username '%s' is already bound to another Dune query", binding.TwitterUserName)
	}
	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing Twitter username: %w", err)
	}

	// Check if Dune query ID already exists
	var existingByQuery db.DuneTwitterBinding
	err = s.db.DB.Where("dune_query_id = ?", binding.DuneQueryID).First(&existingByQuery).Error
	if err == nil {
		return fmt.Errorf("dune query ID '%s' is already bound to another Twitter user", binding.DuneQueryID)
	}
	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing Dune query ID: %w", err)
	}

	// Create new binding
	if err := s.db.DB.Create(binding).Error; err != nil {
		return fmt.Errorf("failed to create binding: %w", err)
	}

	log.Info().
		Str("dune_query_id", binding.DuneQueryID).
		Str("twitter_user_name", binding.TwitterUserName).
		Msg("Created new Dune Twitter binding")

	return nil
}

// GetBinding retrieves a binding by ID
func (s *DuneService) GetBinding(id int64) (*db.DuneTwitterBinding, error) {
	var binding db.DuneTwitterBinding
	if err := s.db.DB.First(&binding, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get binding: %w", err)
	}
	return &binding, nil
}

// GetBindingByTwitterUser retrieves bindings by Twitter username
func (s *DuneService) GetBindingByTwitterUser(twitterUserName string) ([]*db.DuneTwitterBinding, error) {
	var bindings []*db.DuneTwitterBinding
	if err := s.db.DB.Where("twitter_user_name = ?", twitterUserName).Find(&bindings).Error; err != nil {
		return nil, fmt.Errorf("failed to get bindings by Twitter user: %w", err)
	}
	return bindings, nil
}

// GetAllBindings retrieves all bindings with pagination
func (s *DuneService) GetAllBindings(limit, offset int) ([]*db.DuneTwitterBinding, error) {
	var bindings []*db.DuneTwitterBinding
	if err := s.db.DB.Limit(limit).Offset(offset).Find(&bindings).Error; err != nil {
		return nil, fmt.Errorf("failed to get all bindings: %w", err)
	}
	return bindings, nil
}

// GetAllBindingsWithoutPagination retrieves all bindings without pagination
func (s *DuneService) GetAllBindingsWithoutPagination() ([]*db.DuneTwitterBinding, error) {
	var bindings []*db.DuneTwitterBinding
	if err := s.db.DB.Find(&bindings).Error; err != nil {
		return nil, fmt.Errorf("failed to get all bindings: %w", err)
	}
	return bindings, nil
}

// UpdateBinding updates an existing binding
func (s *DuneService) UpdateBinding(id int64, updates map[string]interface{}) error {
	// Get the current binding to check what we're updating
	currentBinding, err := s.GetBinding(id)
	if err != nil {
		return fmt.Errorf("failed to get current binding: %w", err)
	}
	if currentBinding == nil {
		return fmt.Errorf("binding with ID %d not found", id)
	}

	// Check for conflicts if Twitter username is being updated
	if newTwitterUserName, exists := updates["twitter_user_name"]; exists {
		if newTwitterUserName != currentBinding.TwitterUserName {
			var existingByTwitter db.DuneTwitterBinding
			err := s.db.DB.Where("twitter_user_name = ? AND id != ?", newTwitterUserName, id).First(&existingByTwitter).Error
			if err == nil {
				return fmt.Errorf("twitter username '%s' is already bound to another Dune query", newTwitterUserName)
			}
			if err != gorm.ErrRecordNotFound {
				return fmt.Errorf("failed to check existing Twitter username: %w", err)
			}
		}
	}

	// Check for conflicts if Dune query ID is being updated
	if newDuneQueryID, exists := updates["dune_query_id"]; exists {
		if newDuneQueryID != currentBinding.DuneQueryID {
			var existingByQuery db.DuneTwitterBinding
			err := s.db.DB.Where("dune_query_id = ? AND id != ?", newDuneQueryID, id).First(&existingByQuery).Error
			if err == nil {
				return fmt.Errorf("dune query ID '%s' is already bound to another Twitter user", newDuneQueryID)
			}
			if err != gorm.ErrRecordNotFound {
				return fmt.Errorf("failed to check existing Dune query ID: %w", err)
			}
		}
	}

	// Perform the update
	result := s.db.DB.Model(&db.DuneTwitterBinding{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update binding: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("binding with ID %d not found", id)
	}

	log.Info().Int64("id", id).Msg("Updated Dune Twitter binding")
	return nil
}

// DeleteBinding deletes a binding by ID
func (s *DuneService) DeleteBinding(id int64) error {
	result := s.db.DB.Delete(&db.DuneTwitterBinding{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete binding: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("binding with ID %d not found", id)
	}

	log.Info().Int64("id", id).Msg("Deleted Dune Twitter binding")
	return nil
}

// GetQueryResults retrieves query results for a specific query and date range
func (s *DuneService) GetQueryResults(duneQueryID string, startDate, endDate time.Time) ([]*db.DuneQueryResult, error) {
	var results []*db.DuneQueryResult
	err := s.db.DB.Where("dune_query_id = ? AND query_date >= ? AND query_date <= ?",
		duneQueryID, startDate, endDate).
		Order("query_date ASC").
		Find(&results).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get query results: %w", err)
	}
	return results, nil
}

// GetBatchQueryResults retrieves query results for multiple Dune query IDs within date range
// This method solves the n+1 query problem by fetching all results in a single database query
func (s *DuneService) GetBatchQueryResults(duneQueryIDs []string, startDate, endDate time.Time) (map[string][]*db.DuneQueryResult, error) {
	if len(duneQueryIDs) == 0 {
		return make(map[string][]*db.DuneQueryResult), nil
	}

	var results []*db.DuneQueryResult
	err := s.db.DB.Where("dune_query_id IN ? AND query_date >= ? AND query_date <= ?",
		duneQueryIDs, startDate, endDate).
		Order("dune_query_id ASC, query_date ASC").
		Find(&results).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get batch query results: %w", err)
	}

	// Group results by query ID
	groupedResults := make(map[string][]*db.DuneQueryResult)
	for _, result := range results {
		groupedResults[result.DuneQueryID] = append(groupedResults[result.DuneQueryID], result)
	}

	return groupedResults, nil
}

// GetProjectData retrieves project data by Twitter username and date range
func (s *DuneService) GetProjectData(twitterUserName string, startDate, endDate time.Time, chainIDs []string) (*ProjectDataResponse, error) {
	// Get bindings for the Twitter user
	bindings, err := s.GetBindingByTwitterUser(twitterUserName)
	if err != nil {
		return nil, fmt.Errorf("failed to get bindings: %w", err)
	}

	if len(bindings) == 0 {
		return nil, fmt.Errorf("no bindings found for Twitter user: %s", twitterUserName)
	}

	// Use the first binding for project info
	binding := bindings[0]

	response := &ProjectDataResponse{
		ProjectName:     binding.ProjectName,
		ProjectLogo:     binding.ProjectLogo,
		TwitterUserName: twitterUserName,
		DuneQueryID:     binding.DuneQueryID,
		QueryResults:    []*DuneQueryResultResponse{},
	}

	// Collect all query results from all bindings
	allResults := []*db.DuneQueryResult{}

	// Get query results for each binding
	for _, binding := range bindings {
		// Filter by chain IDs if specified
		if len(chainIDs) > 0 {
			hasMatchingChain := false
			for _, chainID := range chainIDs {
				for _, bindingChainID := range binding.ChainIDs {
					if chainID == bindingChainID {
						hasMatchingChain = true
						break
					}
				}
				if hasMatchingChain {
					break
				}
			}
			if !hasMatchingChain {
				continue
			}
		}

		results, err := s.GetQueryResults(binding.DuneQueryID, startDate, endDate)
		if err != nil {
			log.Error().Err(err).Str("dune_query_id", binding.DuneQueryID).Msg("Failed to get query results")
			continue
		}

		allResults = append(allResults, results...)
	}

	// Filter out today's data (only return data from yesterday and earlier)
	// Use UTC+8 timezone for today calculation
	// location, _ := time.LoadLocation("Asia/Shanghai")
	today := time.Now().UTC().Truncate(24 * time.Hour)
	filteredResults := []*db.DuneQueryResult{}
	for _, result := range allResults {
		resultDate := result.QueryDate.UTC().Truncate(24 * time.Hour)
		// if true {
		if resultDate.Before(today) {
			filteredResults = append(filteredResults, result)
		}
	}

	// Sort all results by query date in descending order (newest first)
	for i := 0; i < len(filteredResults)-1; i++ {
		for j := i + 1; j < len(filteredResults); j++ {
			if filteredResults[i].QueryDate.Before(filteredResults[j].QueryDate) {
				filteredResults[i], filteredResults[j] = filteredResults[j], filteredResults[i]
			}
		}
	}

	// Convert to response format (excluding ID, CreatedAt, UpdatedAt)
	responseResults := make([]*DuneQueryResultResponse, len(filteredResults))
	for i, result := range filteredResults {
		responseResults[i] = &DuneQueryResultResponse{
			QueryDate:           result.QueryDate,
			ContractInteraction: result.ContractInteraction,
			Users:               result.Users,
		}
	}

	response.QueryResults = responseResults
	return response, nil
}

// ProjectDataResponse represents the response for project data queries
type ProjectDataResponse struct {
	ProjectName     string                    `json:"project_name"`
	ProjectLogo     string                    `json:"project_logo"`
	TwitterUserName string                    `json:"twitter_user_name"`
	DuneQueryID     string                    `json:"dune_query_id"`
	QueryResults    []*DuneQueryResultResponse `json:"query_results,omitempty"` // For backward compatibility
	QueryResult     *DuneQueryResultResponse  `json:"query_result,omitempty"`  // New single result field
}

// DuneQueryResultResponse represents the filtered response for Dune query results
type DuneQueryResultResponse struct {
	QueryDate           time.Time `json:"query_date"`
	ContractInteraction int       `json:"contract_interaction"`
	Users               int       `json:"users"`
	ChangeRate          *float64  `json:"change_rate,omitempty"` // Change rate compared to previous period
}

// StartScheduler starts the cron scheduler for periodic Dune data fetching

// FetchAllDuneData is the scheduled task that fetches data for all bindings
func (s *DuneService) FetchAllDuneData() {
	log.Info().Msg("Starting scheduled Dune data fetch")

	start := time.Now()
	defer func() {
		duration := time.Since(start)
		log.Info().Dur("duration", duration).Msg("Completed scheduled Dune data fetch")
	}()

	// Get all unique Dune query IDs
	var queryIDs []string
	err := s.db.DB.Model(&db.DuneTwitterBinding{}).
		Distinct("dune_query_id").
		Pluck("dune_query_id", &queryIDs).Error

	if err != nil {
		log.Error().Err(err).Msg("Failed to get Dune query IDs")
		return
	}

	log.Info().Int("query_count", len(queryIDs)).Msg("Fetching data for Dune queries")

	// Fetch data for each query ID
	for _, queryID := range queryIDs {
		if err := s.fetchDuneQueryData(queryID); err != nil {
			log.Error().Err(err).Str("query_id", queryID).Msg("Failed to fetch Dune query data")
			continue
		}

		// Small delay between requests to avoid rate limiting
		time.Sleep(100 * time.Millisecond)
	}
}

// fetchDuneQueryData fetches data for a specific Dune query
// FetchDuneQueryData fetches and stores Dune query data for a specific query ID
func (s *DuneService) FetchDuneQueryData(queryID string) error {
	return s.fetchDuneQueryData(queryID)
}

func (s *DuneService) fetchDuneQueryData(queryID string) error {
	// Fetch data from Dune API
	duneResponse, err := s.callDuneAPIStructured(queryID)
	if err != nil {
		return fmt.Errorf("failed to call Dune API: %w", err)
	}

	// Convert to JSONB for backward compatibility
	rawData, err := json.Marshal(duneResponse)
	if err != nil {
		return fmt.Errorf("failed to marshal response: %w", err)
	}
	var jsonbData db.JSONB
	if err := json.Unmarshal(rawData, &jsonbData); err != nil {
		return fmt.Errorf("failed to convert to JSONB: %w", err)
	}

	// Process each row in the response
	var savedCount, updatedCount int
	for _, row := range duneResponse.Result.Rows {
		// Parse the date from the row - handle multiple date formats
		var queryDate time.Time
		var err error

		// Try different date formats
		dateFormats := []string{
			"2006-01-02 15:04:05.000 UTC", // Full timestamp with UTC
			"2006-01-02 15:04:05.000",     // Full timestamp without timezone
			"2006-01-02 15:04:05",         // Timestamp without milliseconds
			"2006-01-02",                  // Date only
		}

		for _, format := range dateFormats {
			queryDate, err = time.Parse(format, row.Date)
			if err == nil {
				break
			}
		}

		if err != nil {
			log.Warn().Err(err).Str("date", row.Date).Msg("Failed to parse date, skipping row")
			continue
		}
		queryDate = queryDate.UTC().Truncate(24 * time.Hour)

		// Check if record already exists for this queryID + date
		var existing db.DuneQueryResult
		err = s.db.DB.Where("dune_query_id = ? AND query_date = ?", queryID, queryDate).
			First(&existing).Error

		if err == nil {
			// Record exists, update it with new data
			existing.ContractInteraction = row.ContractInteraction
			existing.Users = row.Users
			if err = s.db.DB.Save(&existing).Error; err != nil {
				log.Error().Err(err).Str("query_id", queryID).Time("date", queryDate).Msg("Failed to update existing record")
				continue
			}
			updatedCount++
		} else if errors.Is(err, gorm.ErrRecordNotFound) {
			// Record doesn't exist, create new one
			result := &db.DuneQueryResult{
				DuneQueryID:         queryID,
				QueryDate:           queryDate,
				ContractInteraction: row.ContractInteraction,
				Users:               row.Users,
			}
			if err = s.db.DB.Create(result).Error; err != nil {
				log.Error().Err(err).Str("query_id", queryID).Time("date", queryDate).Msg("Failed to create new record")
				continue
			}
			savedCount++
		} else {
			log.Error().Err(err).Str("query_id", queryID).Time("date", queryDate).Msg("Failed to check existing record")
			continue
		}
	}

	log.Info().
		Str("query_id", queryID).
		Int("saved_count", savedCount).
		Int("updated_count", updatedCount).
		Int("total_rows", len(duneResponse.Result.Rows)).
		Msg("Successfully processed Dune query results")
	return nil
}

// callDuneAPIStructured calls the Dune API and returns structured response
func (s *DuneService) callDuneAPIStructured(queryID string) (*DuneQueryResults, error) {
	// Construct API URL
	apiURL := fmt.Sprintf("%s/api/v1/query/%s/results?limit=30", s.config.BaseURL, queryID)

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// Create request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add headers if needed (API key, etc.)
	req.Header.Set("X-Dune-API-Key", s.config.APIKey)

	// Make request with retry logic
	var resp *http.Response
	for i := 0; i < s.config.MaxRetries; i++ {
		resp, err = client.Do(req)
		if err != nil {
			if i == s.config.MaxRetries-1 {
				return nil, fmt.Errorf("failed to make request after %d retries: %w", s.config.MaxRetries, err)
			}
			log.Warn().Err(err).Int("retry", i+1).Str("query_id", queryID).Msg("Request failed, retrying")
			time.Sleep(time.Duration(i+1) * time.Second)
			continue
		}
		break
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status code: %d", resp.StatusCode)
	}

	// Parse response
	var result DuneQueryResults
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

// GetAllProjectsYesterdayData retrieves yesterday's data for all binding projects
func (s *DuneService) GetAllProjectsYesterdayData() ([]*ProjectDataResponse, error) {
	// Calculate yesterday's date range (UTC)
	yesterday := time.Now().UTC().AddDate(0, 0, -1).Truncate(24 * time.Hour)
	startDate := yesterday
	endDate := yesterday.Add(24 * time.Hour)

	return s.GetAllProjectsDataWithTimeRange(startDate, endDate)
}

// GetAllProjectsDataWithTimeRange retrieves data for all binding projects within specified time range
// with year-over-year comparison analysis (backward compatibility)
func (s *DuneService) GetAllProjectsDataWithTimeRange(startDate, endDate time.Time) ([]*ProjectDataResponse, error) {
	log.Info().
		Time("start_date", startDate).
		Time("end_date", endDate).
		Msg("Getting all projects data with time range")

	// Validate input parameters
	if startDate.After(endDate) {
		return nil, fmt.Errorf("start date cannot be after end date")
	}

	// Get all bindings without pagination
	bindings, err := s.GetAllBindingsWithoutPagination()
	if err != nil {
		return nil, fmt.Errorf("failed to get all bindings: %w", err)
	}

	if len(bindings) == 0 {
		log.Info().Msg("No bindings found, returning empty results")
		return []*ProjectDataResponse{}, nil
	}

	// Calculate time range duration for comparison period
	duration := endDate.Sub(startDate)

	// Calculate comparison period (previous period of same duration)
	comparisonEndDate := startDate.Add(-time.Hour) // End just before current period starts
	comparisonStartDate := comparisonEndDate.Add(-duration)

	log.Info().
		Time("comparison_start_date", comparisonStartDate).
		Time("comparison_end_date", comparisonEndDate).
		Dur("period_duration", duration).
		Msg("Calculated comparison period for year-over-year analysis")

	// Collect all unique Dune query IDs for batch processing
	duneQueryIDs := make([]string, 0, len(bindings))
	bindingMap := make(map[string]*db.DuneTwitterBinding)

	for _, binding := range bindings {
		duneQueryIDs = append(duneQueryIDs, binding.DuneQueryID)
		bindingMap[binding.DuneQueryID] = binding
	}

	// Batch fetch current period results
	currentResults, err := s.GetBatchQueryResults(duneQueryIDs, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get current period batch query results: %w", err)
	}

	// Batch fetch comparison period results
	comparisonResults, err := s.GetBatchQueryResults(duneQueryIDs, comparisonStartDate, comparisonEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get comparison period batch query results: %w", err)
	}

	log.Info().
		Int("bindings_count", len(bindings)).
		Int("current_results_count", len(currentResults)).
		Int("comparison_results_count", len(comparisonResults)).
		Msg("Retrieved batch query results")

	// Build response for each binding
	responses := make([]*ProjectDataResponse, 0, len(bindings))

	for _, binding := range bindings {
		response := &ProjectDataResponse{
			ProjectName:     binding.ProjectName,
			ProjectLogo:     binding.ProjectLogo,
			TwitterUserName: binding.TwitterUserName,
			DuneQueryID:     binding.DuneQueryID,
			QueryResults:    []*DuneQueryResultResponse{},
		}

		// Get current period results for this binding
		currentQueryResults := currentResults[binding.DuneQueryID]

		// Get comparison period results for this binding
		comparisonQueryResults := comparisonResults[binding.DuneQueryID]

		// Calculate aggregated metrics for comparison
		currentTotalInteractions, currentTotalUsers := s.aggregateResults(currentQueryResults)
		comparisonTotalInteractions, comparisonTotalUsers := s.aggregateResults(comparisonQueryResults)

		// Convert current results to response format with change rate calculation
		for _, result := range currentQueryResults {
			resultResponse := &DuneQueryResultResponse{
				QueryDate:           result.QueryDate,
				ContractInteraction: result.ContractInteraction,
				Users:               result.Users,
			}

			// Calculate change rate for contract interactions
			if comparisonTotalInteractions > 0 {
				changeRate := float64(currentTotalInteractions-comparisonTotalInteractions) / float64(comparisonTotalInteractions) * 100
				resultResponse.ChangeRate = &changeRate
			}

			response.QueryResults = append(response.QueryResults, resultResponse)
		}

		// Log metrics for this binding
		log.Debug().
			Str("dune_query_id", binding.DuneQueryID).
			Str("twitter_user_name", binding.TwitterUserName).
			Int("current_interactions", currentTotalInteractions).
			Int("current_users", currentTotalUsers).
			Int("comparison_interactions", comparisonTotalInteractions).
			Int("comparison_users", comparisonTotalUsers).
			Int("results_count", len(response.QueryResults)).
			Msg("Processed binding data with comparison analysis")

		responses = append(responses, response)
	}

	log.Info().
		Int("total_responses", len(responses)).
		Msg("Successfully completed GetAllProjectsDataWithTimeRange")

	return responses, nil
}

// GetAllProjectsYesterdayDataWithComparison retrieves yesterday's data for all binding projects
// with comparison to a specific date
func (s *DuneService) GetAllProjectsYesterdayDataWithComparison(comparisonDate time.Time) ([]*ProjectDataResponse, error) {
	// Calculate yesterday's date in UTC+8 timezone
	utcPlus8 := time.FixedZone("UTC+8", 8*60*60)
	now := time.Now().In(utcPlus8)
	yesterday := now.AddDate(0, 0, -1)

	// Convert to UTC date range for database query (whole day)
	yesterdayStart := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, time.UTC)
	yesterdayEnd := yesterdayStart.Add(24 * time.Hour)

	// Convert comparison date to UTC date range (whole day)
	comparisonStart := time.Date(comparisonDate.Year(), comparisonDate.Month(), comparisonDate.Day(), 0, 0, 0, 0, time.UTC)
	comparisonEnd := comparisonStart.Add(24 * time.Hour)

	log.Info().
		Time("yesterday_start", yesterdayStart).
		Time("yesterday_end", yesterdayEnd).
		Time("comparison_start", comparisonStart).
		Time("comparison_end", comparisonEnd).
		Msg("Getting all projects yesterday data with comparison")

	// Get all bindings without pagination
	bindings, err := s.GetAllBindingsWithoutPagination()
	if err != nil {
		return nil, fmt.Errorf("failed to get all bindings: %w", err)
	}

	if len(bindings) == 0 {
		log.Info().Msg("No bindings found, returning empty results")
		return []*ProjectDataResponse{}, nil
	}

	// Collect all unique Dune query IDs for batch processing
	duneQueryIDs := make([]string, 0, len(bindings))

	for _, binding := range bindings {
		duneQueryIDs = append(duneQueryIDs, binding.DuneQueryID)
	}

	// Batch fetch yesterday's results
	yesterdayResults, err := s.GetBatchQueryResults(duneQueryIDs, yesterdayStart, yesterdayEnd)
	if err != nil {
		return nil, fmt.Errorf("failed to get yesterday's batch query results: %w", err)
	}

	// Batch fetch comparison date results
	comparisonResults, err := s.GetBatchQueryResults(duneQueryIDs, comparisonStart, comparisonEnd)
	if err != nil {
		return nil, fmt.Errorf("failed to get comparison date batch query results: %w", err)
	}

	log.Info().
		Int("bindings_count", len(bindings)).
		Int("yesterday_results_count", len(yesterdayResults)).
		Int("comparison_results_count", len(comparisonResults)).
		Msg("Retrieved batch query results for yesterday and comparison")

	// Build response for each binding
	responses := make([]*ProjectDataResponse, 0, len(bindings))

	for _, binding := range bindings {
		response := &ProjectDataResponse{
			ProjectName:     binding.ProjectName,
			ProjectLogo:     binding.ProjectLogo,
			TwitterUserName: binding.TwitterUserName,
			DuneQueryID:     binding.DuneQueryID,
		}

		// Get yesterday's results for this binding
		yesterdayQueryResults := yesterdayResults[binding.DuneQueryID]

		// Get comparison results for this binding
		comparisonQueryResults := comparisonResults[binding.DuneQueryID]

		// Calculate aggregated metrics
		yesterdayTotalInteractions, yesterdayTotalUsers := s.aggregateResults(yesterdayQueryResults)
		comparisonTotalInteractions, comparisonTotalUsers := s.aggregateResults(comparisonQueryResults)

		// Create single result response if we have yesterday's data
		if len(yesterdayQueryResults) > 0 {
			// Use the first result's date (should be yesterday)
			resultResponse := &DuneQueryResultResponse{
				QueryDate:           yesterdayQueryResults[0].QueryDate,
				ContractInteraction: yesterdayTotalInteractions,
				Users:               yesterdayTotalUsers,
			}

			// Calculate change rate for contract interactions
			if comparisonTotalInteractions > 0 {
				changeRate := float64(yesterdayTotalInteractions-comparisonTotalInteractions) / float64(comparisonTotalInteractions) * 100
				resultResponse.ChangeRate = &changeRate
			}

			response.QueryResult = resultResponse
		}

		// Log metrics for this binding
		log.Debug().
			Str("dune_query_id", binding.DuneQueryID).
			Str("twitter_user_name", binding.TwitterUserName).
			Int("yesterday_interactions", yesterdayTotalInteractions).
			Int("yesterday_users", yesterdayTotalUsers).
			Int("comparison_interactions", comparisonTotalInteractions).
			Int("comparison_users", comparisonTotalUsers).
			Msg("Processed binding data with yesterday vs comparison analysis")

		responses = append(responses, response)
	}

	log.Info().
		Int("total_responses", len(responses)).
		Msg("Successfully completed GetAllProjectsYesterdayDataWithComparison")

	return responses, nil
}

// aggregateResults calculates total contract interactions and users from query results
func (s *DuneService) aggregateResults(results []*db.DuneQueryResult) (totalInteractions, totalUsers int) {
	for _, result := range results {
		totalInteractions += result.ContractInteraction
		totalUsers += result.Users
	}
	return totalInteractions, totalUsers
}



// FetchProjectInfo fetches project information using external APIs
func (s *DuneService) FetchProjectInfo(twitterUserName string) (string, string, error) {
	// This method should call the handlers.go methods at lines 1219 and 1242
	// For now, we'll return empty values and implement this when integrating with handlers
	return "", "", nil
}
