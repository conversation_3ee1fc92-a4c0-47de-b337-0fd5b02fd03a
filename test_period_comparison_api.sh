#!/bin/bash

# Test script for the new period comparison API
# This script tests the /api/dune/projects/period-comparison endpoint

BASE_URL="http://localhost:8080/api"

echo "Testing Dune Period Comparison API"
echo "=================================="

# Test 1: Test with missing days parameter
echo "1. Testing with missing days parameter (should fail):"
curl -s "$BASE_URL/dune/projects/period-comparison" | jq .

echo -e "\n"

# Test 2: Test with invalid days parameter
echo "2. Testing with invalid days parameter (should fail):"
curl -s "$BASE_URL/dune/projects/period-comparison?days=invalid" | jq .

echo -e "\n"

# Test 3: Test with zero days (should fail)
echo "3. Testing with zero days (should fail):"
curl -s "$BASE_URL/dune/projects/period-comparison?days=0" | jq .

echo -e "\n"

# Test 4: Test with negative days (should fail)
echo "4. Testing with negative days (should fail):"
curl -s "$BASE_URL/dune/projects/period-comparison?days=-5" | jq .

echo -e "\n"

# Test 5: Test with 1 day period (should succeed)
echo "5. Testing with 1 day period (should succeed):"
echo "   Current period: yesterday"
echo "   Comparison period: day before yesterday"
curl -s "$BASE_URL/dune/projects/period-comparison?days=1" | jq .

echo -e "\n"

# Test 6: Test with 7 days period (should succeed)
echo "6. Testing with 7 days period (should succeed):"
echo "   Current period: last 7 days (excluding today)"
echo "   Comparison period: 7 days before that"
curl -s "$BASE_URL/dune/projects/period-comparison?days=7" | jq .

echo -e "\n"

# Test 7: Test with 14 days period (should succeed)
echo "7. Testing with 14 days period (should succeed):"
echo "   Current period: last 14 days (excluding today)"
echo "   Comparison period: 14 days before that"
curl -s "$BASE_URL/dune/projects/period-comparison?days=14" | jq .

echo -e "\n"

# Test 8: Test with 30 days period (should succeed)
echo "8. Testing with 30 days period (should succeed):"
echo "   Current period: last 30 days (excluding today)"
echo "   Comparison period: 30 days before that"
curl -s "$BASE_URL/dune/projects/period-comparison?days=30" | jq .

echo -e "\n"

# Test 9: Test the old time range API for comparison (should still work)
echo "9. Testing the old time range API for comparison:"
START_TIME=$(date -d '2 days ago' +%s)
END_TIME=$(date -d '1 day ago' +%s)
curl -s "$BASE_URL/dune/projects/data?start_time=$START_TIME&end_time=$END_TIME" | jq .

echo -e "\n"

echo "Test completed!"
echo ""
echo "Expected behavior:"
echo "- Tests 1-4 should return 400 Bad Request with error messages"
echo "- Tests 5-8 should return 200 OK with project data including query_result field"
echo "- Test 9 shows the old API format with query_results array for comparison"
echo ""
echo "Note: Make sure the server is running on $BASE_URL before running this test."
echo "You can start the server with: make run or go run cmd/main.go"
echo ""
echo "Key features of the new API:"
echo "- Uses 'days' parameter instead of 'comparison_time'"
echo "- Automatically calculates current and comparison periods"
echo "- Returns aggregated data for each period"
echo "- Uses 'query_result' (single object) instead of 'query_results' (array)"
echo "- Calculates change rate as percentage"
echo ""
echo "Example usage:"
echo "- days=1: Compare yesterday vs day before yesterday"
echo "- days=7: Compare last 7 days vs previous 7 days"
echo "- days=30: Compare last 30 days vs previous 30 days"
