#!/bin/bash

# Test script for the new yesterday comparison API
# This script tests the /api/dune/projects/yesterday-comparison endpoint

BASE_URL="http://localhost:8080/api"

echo "Testing Dune Yesterday Comparison API"
echo "======================================"

# Test 1: Test with missing comparison_time parameter
echo "1. Testing with missing comparison_time parameter (should fail):"
curl -s "$BASE_URL/dune/projects/yesterday-comparison" | jq .

echo -e "\n"

# Test 2: Test with invalid comparison_time parameter
echo "2. Testing with invalid comparison_time parameter (should fail):"
curl -s "$BASE_URL/dune/projects/yesterday-comparison?comparison_time=invalid" | jq .

echo -e "\n"

# Test 3: Test with valid comparison_time (7 days ago)
echo "3. Testing with valid comparison_time (7 days ago):"
COMPARISON_TIME=$(date -d '7 days ago' +%s)
echo "Comparison time: $COMPARISON_TIME ($(date -d @$COMPARISON_TIME))"
curl -s "$BASE_URL/dune/projects/yesterday-comparison?comparison_time=$COMPARISON_TIME" | jq .

echo -e "\n"

# Test 4: Test with comparison_time (1 month ago)
echo "4. Testing with comparison_time (1 month ago):"
COMPARISON_TIME=$(date -d '1 month ago' +%s)
echo "Comparison time: $COMPARISON_TIME ($(date -d @$COMPARISON_TIME))"
curl -s "$BASE_URL/dune/projects/yesterday-comparison?comparison_time=$COMPARISON_TIME" | jq .

echo -e "\n"

# Test 5: Test with comparison_time (same day last week)
echo "5. Testing with comparison_time (same day last week):"
COMPARISON_TIME=$(date -d 'last week' +%s)
echo "Comparison time: $COMPARISON_TIME ($(date -d @$COMPARISON_TIME))"
curl -s "$BASE_URL/dune/projects/yesterday-comparison?comparison_time=$COMPARISON_TIME" | jq .

echo -e "\n"

# Test 6: Test with comparison_time (2 weeks ago)
echo "6. Testing with comparison_time (2 weeks ago):"
COMPARISON_TIME=$(date -d '2 weeks ago' +%s)
echo "Comparison time: $COMPARISON_TIME ($(date -d @$COMPARISON_TIME))"
curl -s "$BASE_URL/dune/projects/yesterday-comparison?comparison_time=$COMPARISON_TIME" | jq .

echo -e "\n"

# Test 7: Compare with old API for reference
echo "7. Testing old yesterday API for comparison:"
curl -s "$BASE_URL/dune/projects/yesterday" | jq .

echo -e "\n"

echo "Test completed!"
echo ""
echo "Expected behavior:"
echo "- Tests 1-2 should return 400 Bad Request with error messages"
echo "- Tests 3-6 should return 200 OK with project data including query_result field"
echo "- Test 7 shows the old API format with query_results array for comparison"
echo ""
echo "Note: Make sure the server is running on $BASE_URL before running this test."
echo "You can start the server with: make run or go run cmd/main.go"
echo ""
echo "Key differences in the new API:"
echo "- Uses 'query_result' (single object) instead of 'query_results' (array)"
echo "- Automatically calculates yesterday's data in UTC+8 timezone"
echo "- Compares with the specified comparison_time parameter"
echo "- Returns change_rate as percentage (e.g., 25.5 for 25.5% increase)"
