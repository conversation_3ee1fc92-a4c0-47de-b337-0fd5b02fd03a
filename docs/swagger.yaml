basePath: /api
definitions:
  api.CAResponse:
    properties:
      address:
        type: string
      chain_type:
        type: string
      is_recognized:
        type: boolean
      tags:
        items:
          type: string
        type: array
      token_details:
        description: Changed to array
        items:
          $ref: '#/definitions/api.TokenDetailsResponse'
        type: array
      trade_token_details:
        items:
          $ref: '#/definitions/api.TradeTokenInfo'
        type: array
      tweets:
        items:
          $ref: '#/definitions/api.TweetInfo'
        type: array
    type: object
  api.DuneBindingRequest:
    description: Request body for creating or updating a Dune Twitter binding
    properties:
      chain_names:
        example:
        - base
        - bsc
        items:
          type: string
        type: array
      dune_query_id:
        example: "3234567"
        type: string
      twitter_user_name:
        example: elonmusk
        type: string
    required:
    - dune_query_id
    - twitter_user_name
    type: object
  api.GetTradeTokenInfoResponse:
    properties:
      code:
        type: integer
      data:
        properties:
          list:
            items:
              $ref: '#/definitions/api.TradeTokenInfo'
            type: array
          page:
            type: integer
          page_size:
            type: integer
          total_count:
            type: integer
        type: object
      msg:
        type: string
    type: object
  api.NoticeResponse:
    properties:
      is_business_data:
        type: boolean
      is_ecosystem_partnership:
        type: boolean
      is_industry_event:
        type: boolean
      is_others:
        type: boolean
      is_product_update:
        type: boolean
      is_profit_opportunity:
        type: boolean
    type: object
  api.TokenDetailsResponse:
    properties:
      chain_id:
        type: string
      holder_count:
        type: integer
      logo_url:
        type: string
      market_cap_usd:
        type: number
      name:
        type: string
      pair_created_at:
        type: integer
      price_usd:
        type: number
      source:
        description: Added source field
        type: string
      symbol:
        type: string
      twitter_url:
        type: string
    type: object
  api.TradeTokenInfo:
    properties:
      address:
        type: string
      ai_report:
        type: string
      banner_url:
        type: string
      chain_id:
        type: integer
      coingecko_url:
        type: string
      coinmarketcap_url:
        type: string
      creation_date:
        type: string
      creator_x_username:
        type: string
      decimals:
        type: integer
      description:
        type: string
      discord_url:
        type: string
      followers_count:
        type: integer
      github_url:
        type: string
      influencers_count:
        type: integer
      instagram_username:
        type: string
      is_verified:
        type: boolean
      is_watched:
        type: boolean
      logo_url:
        type: string
      market_cap:
        type: string
      medium_url:
        type: string
      mobile_banner_url:
        type: string
      name:
        type: string
      price_change_in_1hours:
        type: string
      price_change_in_6hours:
        type: string
      price_change_in_24hours:
        type: string
      price_in_usd:
        type: string
      profile:
        type: string
      project_url:
        type: string
      projects_count:
        type: integer
      rank:
        type: integer
      reddit_url:
        type: string
      research_report:
        type: string
      slug:
        type: string
      status:
        type: integer
      symbol:
        type: string
      tags:
        items:
          properties:
            color:
              type: string
            name:
              type: string
            rank:
              type: integer
            type:
              type: integer
          type: object
        type: array
      telegram_url:
        type: string
      tiktok_url:
        type: string
      top_20_followers:
        items:
          properties:
            avatar:
              type: string
            name:
              type: string
            username:
              type: string
          type: object
        type: array
      total_buy_count_24hours:
        type: string
      total_buyer_count_24hours:
        type: string
      total_liquidity:
        type: string
      total_makers_count_24hours:
        type: string
      total_sell_count_24hours:
        type: string
      total_seller_count_24hours:
        type: string
      total_supply:
        type: string
      total_tx_count_24hours:
        type: string
      total_volume_in_1hours:
        type: string
      total_volume_in_6hours:
        type: string
      total_volume_in_24hours:
        type: string
      twitter_score:
        type: string
      twitter_user_id:
        type: string
      twitter_username:
        type: string
      type:
        type: integer
      venture_capitals_count:
        type: integer
      warpcast_url:
        type: string
    type: object
  api.TweetInfo:
    properties:
      article_cover_url:
        type: string
      article_preview_text:
        type: string
      article_title:
        type: string
      bookmark_count:
        type: integer
      bullet_points: {}
      collection_tags:
        items:
          type: string
        type: array
      content_type:
        description: '"tweet" or "article"'
        type: string
      favorite_count:
        type: integer
      id:
        type: string
      images:
        items:
          type: string
        type: array
      notices:
        $ref: '#/definitions/api.NoticeResponse'
      published_at:
        type: integer
      reply_count:
        type: integer
      retweet_count:
        type: integer
      source_list_type:
        description: '"KOLs" or "Projects"'
        type: string
      tags:
        items:
          type: string
        type: array
      text:
        type: string
      user:
        $ref: '#/definitions/api.UserResponse'
      views_count:
        type: integer
    type: object
  api.TweetResponse:
    properties:
      article_cover_url:
        type: string
      article_preview_text:
        type: string
      article_title:
        type: string
      bookmark_count:
        type: integer
      bullet_points: {}
      collection_tags:
        items:
          type: string
        type: array
      content_type:
        description: '"tweet" or "article"'
        type: string
      contract_addresses:
        items:
          $ref: '#/definitions/api.CAResponse'
        type: array
      favorite_count:
        type: integer
      id:
        type: string
      images:
        items:
          type: string
        type: array
      notices:
        $ref: '#/definitions/api.NoticeResponse'
      published_at:
        type: integer
      reply_count:
        type: integer
      retweet_count:
        type: integer
      source_list_type:
        description: '"KOLs" or "Projects"'
        type: string
      tags:
        items:
          type: string
        type: array
      text:
        type: string
      user:
        $ref: '#/definitions/api.UserResponse'
      views_count:
        type: integer
    type: object
  api.TwitterUserInfo:
    properties:
      name:
        type: string
      profile_image_url:
        type: string
      screen_name:
        type: string
      tags:
        items:
          type: string
        type: array
      user_id:
        type: string
    type: object
  api.UserAnnouncementStatsResponse:
    properties:
      business_data_count:
        type: integer
      ecosystem_partnership_count:
        type: integer
      industry_events_count:
        type: integer
      others_count:
        type: integer
      product_updates_count:
        type: integer
      profit_opportunity_count:
        type: integer
      total_announcements_count:
        type: integer
      twitter_user:
        $ref: '#/definitions/api.TwitterUserInfo'
    type: object
  api.UserResponse:
    properties:
      followers_count:
        type: integer
      id:
        type: string
      is_verified:
        type: boolean
      name:
        type: string
      profile_image_url:
        type: string
      screen_name:
        type: string
    type: object
  db.DuneTwitterBinding:
    properties:
      chain_ids:
        items:
          type: string
        type: array
      dune_query_id:
        type: string
      id:
        type: integer
      project_logo:
        type: string
      project_name:
        type: string
      twitter_user_name:
        type: string
    type: object
  services.DuneQueryResultResponse:
    properties:
      change_rate:
        description: Change rate compared to previous period
        type: number
      contract_interaction:
        type: integer
      query_date:
        type: string
      users:
        type: integer
    type: object
  services.ProjectDataResponse:
    properties:
      dune_query_id:
        type: string
      project_logo:
        type: string
      project_name:
        type: string
      query_result:
        allOf:
        - $ref: '#/definitions/services.DuneQueryResultResponse'
        description: New single result field
      query_results:
        description: For backward compatibility
        items:
          $ref: '#/definitions/services.DuneQueryResultResponse'
        type: array
      twitter_user_name:
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
  description: API for the Real-Time Contract Address service
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Real-Time CA Service API
  version: "1.0"
paths:
  /announcement-statistics:
    get:
      consumes:
      - application/json
      description: Get statistics for tweets with source_list_type="Projects" and
        ai_judgment="YES" grouped by Twitter user. Optional time filtering with start_time
        and end_time query parameters (Unix timestamps). Optional sorting with sort_field
        and sort_direction parameters.
      parameters:
      - description: Start time (Unix timestamp, inclusive)
        example: **********
        in: query
        name: start_time
        type: integer
      - description: End time (Unix timestamp, inclusive)
        example: **********
        in: query
        name: end_time
        type: integer
      - description: Field to sort by
        enum:
        - product_updates
        - business_data
        - ecosystem_partnership
        - profit_opportunity
        - industry_events
        - others
        - total
        in: query
        name: sort_field
        type: string
      - description: Sort direction
        enum:
        - asc
        - desc
        in: query
        name: sort_direction
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.UserAnnouncementStatsResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get announcement statistics
      tags:
      - statistics
  /cached-collections:
    get:
      consumes:
      - application/json
      description: Retrieves collections data from Redis cache with tags information
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Page size (default: 100)'
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GetTradeTokenInfoResponse'
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get cached collections data
      tags:
      - collections
  /collection-tags:
    get:
      consumes:
      - application/json
      description: Returns a list of all unique collection tags from cached collections
      produces:
      - application/json
      responses:
        "200":
          description: Collection tags list
          schema:
            items:
              type: string
            type: array
        "500":
          description: Internal server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get all collection tags
      tags:
      - collections
  /dune/bindings:
    get:
      consumes:
      - application/json
      description: Retrieve all Dune Twitter bindings with pagination support
      parameters:
      - description: 'Number of bindings to return (default: 20)'
        in: query
        name: limit
        type: integer
      - description: 'Number of bindings to skip (default: 0)'
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of Dune Twitter bindings
          schema:
            items:
              $ref: '#/definitions/db.DuneTwitterBinding'
            type: array
        "500":
          description: Failed to get bindings
          schema:
            additionalProperties: true
            type: object
      summary: Get all Dune Twitter bindings
      tags:
      - dune
    post:
      consumes:
      - application/json
      description: Create a new binding between a Dune query and a Twitter user
      parameters:
      - description: Dune binding request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.DuneBindingRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Binding created successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request body
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to create binding
          schema:
            additionalProperties: true
            type: object
      summary: Create a new Dune Twitter binding
      tags:
      - dune
  /dune/bindings/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a Dune Twitter binding by its ID
      parameters:
      - description: Binding ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Binding deleted successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid binding ID
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to delete binding
          schema:
            additionalProperties: true
            type: object
      summary: Delete a Dune Twitter binding
      tags:
      - dune
    get:
      consumes:
      - application/json
      description: Retrieve a specific Dune Twitter binding by its ID
      parameters:
      - description: Binding ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Dune Twitter binding
          schema:
            $ref: '#/definitions/db.DuneTwitterBinding'
        "400":
          description: Invalid binding ID
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Binding not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get binding
          schema:
            additionalProperties: true
            type: object
      summary: Get a specific Dune Twitter binding
      tags:
      - dune
    put:
      consumes:
      - application/json
      description: Update an existing Dune Twitter binding by its ID
      parameters:
      - description: Binding ID
        in: path
        name: id
        required: true
        type: integer
      - description: Updated binding data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.DuneBindingRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Binding updated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid binding ID or request body
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to update binding
          schema:
            additionalProperties: true
            type: object
      summary: Update a Dune Twitter binding
      tags:
      - dune
  /dune/project-data:
    get:
      consumes:
      - application/json
      description: Retrieve project data from Dune Analytics by Twitter username and
        time range
      parameters:
      - description: Twitter username
        in: query
        name: twitter_user_name
        required: true
        type: string
      - description: Start time (Unix timestamp)
        in: query
        name: start_time
        type: string
      - description: End time (Unix timestamp)
        in: query
        name: end_time
        type: string
      - description: Comma-separated chain IDs
        in: query
        name: chain_ids
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Project data from Dune Analytics
          schema:
            $ref: '#/definitions/services.ProjectDataResponse'
        "400":
          description: Invalid parameters
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get project data
          schema:
            additionalProperties: true
            type: object
      summary: Get Dune project data
      tags:
      - dune
  /dune/projects/period-comparison:
    get:
      consumes:
      - application/json
      description: Retrieve aggregated data for all Twitter-Dune binding projects
        within specified days range and compare with previous period
      parameters:
      - description: Number of days for the analysis period (e.g., 7 for last 7 days)
        in: query
        name: days
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Projects aggregated data with period comparison
          schema:
            items:
              $ref: '#/definitions/services.ProjectDataResponse'
            type: array
        "400":
          description: Invalid parameters
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get projects data
          schema:
            additionalProperties: true
            type: object
      summary: Get aggregated data for all Dune projects with period comparison
      tags:
      - dune
  /health:
    get:
      consumes:
      - application/json
      description: Returns 200 OK if the service is healthy
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Health check endpoint
      tags:
      - system
  /list-tweets:
    get:
      consumes:
      - application/json
      description: Retrieves a list of tweets related to AI Agent with filtering options
      parameters:
      - description: 'Number of tweets to return (default: 20)'
        in: query
        name: limit
        type: integer
      - description: 'Offset for pagination (default: 0)'
        in: query
        name: offset
        type: integer
      - description: Filter by content type (tweet, article, ALL)
        in: query
        name: content_type
        type: string
      - description: Filter by source type (KOLs, Projects, ALL)
        in: query
        name: source_type
        type: string
      - description: Filter by notice_type
        in: query
        name: notice_type
        type: string
      - description: Filter by user_id
        in: query
        name: user_id
        type: string
      - description: Filter by user_name
        in: query
        name: user_name
        type: string
      - collectionFormat: csv
        description: Filter by tags (comma-separated, e.g. 'tag1,tag2')
        in: query
        items:
          type: string
        name: tags
        type: array
      - collectionFormat: csv
        description: Filter by collection tags (comma-separated, e.g. 'tag1,tag2')
        in: query
        items:
          type: string
        name: collection_tags
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.TweetResponse'
            type: array
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get AI Agent related tweets
      tags:
      - tweets
  /recognized-ca/{address}/{chain_id}:
    get:
      consumes:
      - application/json
      description: Retrieves a specific recognized contract address by its address
        and chain ID
      parameters:
      - description: Contract address
        in: path
        name: address
        required: true
        type: string
      - description: Chain ID
        in: path
        name: chain_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.CAResponse'
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get a single recognized contract address
      tags:
      - contract-addresses
  /recognized-cas:
    get:
      consumes:
      - application/json
      description: Retrieves a list of recognized contract addresses
      parameters:
      - description: 'Number of CAs to return (default: 20)'
        in: query
        name: limit
        type: integer
      - description: 'Offset for pagination (default: 0)'
        in: query
        name: offset
        type: integer
      - collectionFormat: csv
        description: Filter by tags (comma-separated, e.g. 'tag1,tag2')
        in: query
        items:
          type: string
        name: tags
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.CAResponse'
            type: array
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get recognized contract addresses
      tags:
      - contract-addresses
  /telegram-notification-stats:
    get:
      consumes:
      - application/json
      description: Returns statistics about Telegram notifications including total
        sent, duplicates prevented, etc.
      produces:
      - application/json
      responses:
        "200":
          description: Notification statistics
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get Telegram notification statistics
      tags:
      - notifications
  /trigger-collections-cache:
    post:
      consumes:
      - application/json
      description: Triggers the collections cache update task manually for testing
        purposes
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Manually trigger collections cache update
      tags:
      - system
  /tweet/{tweet_id}:
    get:
      consumes:
      - application/json
      description: Retrieves a single tweet by its ID
      parameters:
      - description: Tweet ID
        in: path
        name: tweet_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.TweetResponse'
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get a tweet by ID
      tags:
      - tweets
  /tweets:
    get:
      consumes:
      - application/json
      description: Retrieves a list of tweets
      parameters:
      - description: 'Number of tweets to return (default: 20)'
        in: query
        name: limit
        type: integer
      - description: 'Offset for pagination (default: 0)'
        in: query
        name: offset
        type: integer
      - collectionFormat: csv
        description: Filter by tags (comma-separated, e.g. 'tag1,tag2')
        in: query
        items:
          type: string
        name: tags
        type: array
      - collectionFormat: csv
        description: Filter by collection tags (comma-separated, e.g. 'tag1,tag2')
        in: query
        items:
          type: string
        name: collection_tags
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.TweetResponse'
            type: array
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get tweets
      tags:
      - tweets
  /webhook/twitter:
    post:
      consumes:
      - application/json
      description: Processes webhook events from Twitter
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Handle Twitter webhook
      tags:
      - webhooks
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
